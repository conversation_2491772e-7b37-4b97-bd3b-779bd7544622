'use client';

import type { Attachment, UIMessage } from 'ai';
import { useChat } from '@ai-sdk/react';
import { useEffect, useState } from 'react';
import useSWR, { useSWRConfig } from 'swr';
import { ChatHeader } from '@/components/chat-header';
import type { Vote } from '@/lib/db/schema';
import { fetchWithErrorHandlers, generateUUID } from '@/lib/utils';
import { Artifact } from './artifact';
import { MultimodalInput } from './multimodal-input';
import { Messages } from './messages';
import type { VisibilityType } from './visibility-selector';
import { useArtifactSelector } from '@/hooks/use-artifact';
import { toast } from './toast';
import type { Session } from 'next-auth';
import { unstable_serialize } from 'swr/infinite';
import { getChatHistoryPaginationKey } from './sidebar-history';
import { SuggestedActions } from './suggested-actions';
import { Greeting } from './greeting';
import { useSearchParams } from 'next/navigation';
import { ChatSDKError } from '@/lib/errors';

export function Chat({
  id,
  initialMessages,
  selectedChatModel,
  selectedVisibilityType,
  isReadonly,
  session,
}: {
  id: string;
  initialMessages: Array<UIMessage>;
  selectedChatModel: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
  session: Session;
}) {
  const { mutate } = useSWRConfig();

  // Filter out messages with incompatible roles for useChat hook and cast to Message type
  const compatibleInitialMessages = initialMessages
    .filter((message) => !['function', 'tool'].includes(message.role))
    .map((message) => {
      const { annotations, ...messageWithoutAnnotations } = message;
      return {
        ...messageWithoutAnnotations,
        role: message.role as 'user' | 'assistant' | 'system' | 'data',
        // Convert annotations to JSONValue format if they exist
        ...(annotations && { annotations: annotations as any }),
      };
    });

  const {
    messages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    append,
    status,
    stop,
    reload,
  } = useChat({
    id,
    body: { id, selectedChatModel: selectedChatModel },
    initialMessages: compatibleInitialMessages,
    experimental_throttle: 250, // Increased throttling to prevent infinite loops
    sendExtraMessageFields: true,
    generateId: generateUUID,
    fetch: async (input: RequestInfo | URL, init?: RequestInit) => {
      let lastError: unknown;
      for (let i = 0; i < 3; i++) {
        try {
          return await fetchWithErrorHandlers(input, init);
        } catch (error) {
          lastError = error;
          if (error instanceof ChatSDKError && error.type === 'rate_limit') {
            break; // Ne pas retry sur rate limit
          }
          if (i < 2) {
            await new Promise((resolve) => setTimeout(resolve, 1000 * (i + 1)));
          }
        }
      }
      throw lastError;
    },
    onFinish: () => {
      mutate(unstable_serialize(getChatHistoryPaginationKey));
    },
    onError: (error) => {
      // Enhanced error handling with ChatSDKError support
      let errorMessage = error.message;
      let errorType: 'error' | 'info' = 'error';

      if (error instanceof ChatSDKError) {
        // Use ChatSDKError's built-in message handling
        errorMessage = error.message;

        // Adjust toast type and message based on error type
        if (error.type === 'rate_limit') {
          errorType = 'info';
          errorMessage =
            'You have reached your message limit. Please try again later.';
        } else if (error.type === 'unauthorized') {
          errorMessage = 'Please sign in to continue chatting.';
        } else if (error.type === 'forbidden') {
          errorMessage = 'You do not have permission to access this chat.';
        } else if (error.type === 'not_found') {
          errorMessage = 'The requested chat could not be found.';
        }
      }

      toast({
        type: errorType,
        description: errorMessage,
      });
    },
  });

  const searchParams = useSearchParams();
  const query = searchParams?.get('query');
  const [hasAppendedQuery, setHasAppendedQuery] = useState(false);

  useEffect(() => {
    if (query && !hasAppendedQuery) {
      append({
        role: 'user',
        content: query,
      });

      setHasAppendedQuery(true);
      window.history.replaceState({}, '', `/chat/${id}`);
    }
  }, [query, append, hasAppendedQuery, id]);

  const { data: votes } = useSWR<Array<Vote>>(
    messages.length >= 2 ? `/api/vote?chatId=${id}` : null,
    async (url: string) => {
      const response = await fetchWithErrorHandlers(url);
      return response.json();
    },
    {
      onError: (error) => {
        // Handle SWR errors with ChatSDKError support
        if (error instanceof ChatSDKError) {
          console.warn('Failed to load votes:', error.message);
          // Don't show toast for votes errors as they're not critical
        } else {
          console.error('Unexpected error loading votes:', error);
        }
      },
    },
  );

  const [attachments, setAttachments] = useState<Array<Attachment>>([]);
  const isArtifactVisible = useArtifactSelector((state) => state.isVisible);

  const messagesWithoutPings = messages.filter(
    (message) => message.content !== 'ping',
  ) as UIMessage[];

  return (
    <>
      <div className="flex flex-col min-w-0 h-dvh bg-background">
        <ChatHeader
          chatId={id}
          selectedModelId={selectedChatModel}
          selectedVisibilityType={selectedVisibilityType}
          isReadonly={isReadonly}
          session={session}
        />

        {messagesWithoutPings.length === 0 ? (
          // Quand il n'y a pas de messages, afficher une structure personnalisée
          <div className="grow flex flex-col items-center justify-center">
            <div className="text-center mb-8">
              {/* Titre du Greeting sans les suggestions */}
              <h1 className="text-2xl font-bold mb-2">
                <Greeting />
              </h1>
            </div>

            <div className="w-full max-w-3xl mx-auto flex flex-col gap-4">
              <form className="flex mx-auto px-4 bg-background gap-2 w-full">
                {!isReadonly && (
                  <MultimodalInput
                    chatId={id}
                    input={input}
                    setInput={setInput}
                    handleSubmit={handleSubmit}
                    status={status}
                    stop={stop}
                    attachments={attachments}
                    setAttachments={setAttachments}
                    messages={messagesWithoutPings}
                    setMessages={setMessages}
                    append={append}
                    showSuggestions={false} // Désactiver les suggestions ici
                  />
                )}
              </form>

              {/* Suggestions directement sous le champ de saisie */}
              <div className="w-full px-4">
                <SuggestedActions append={append} chatId={id} />
              </div>
            </div>
          </div>
        ) : (
          // Structure normale quand il y a des messages
          <>
            <Messages
              chatId={id}
              status={status}
              votes={votes}
              messages={messagesWithoutPings}
              setMessages={setMessages}
              reload={reload}
              isReadonly={isReadonly}
              isArtifactVisible={isArtifactVisible}
            />

            <form className="flex mx-auto px-4 bg-background pb-4 md:pb-6 gap-2 w-full md:max-w-3xl">
              {!isReadonly && (
                <MultimodalInput
                  chatId={id}
                  input={input}
                  setInput={setInput}
                  handleSubmit={handleSubmit}
                  status={status}
                  stop={stop}
                  attachments={attachments}
                  setAttachments={setAttachments}
                  messages={messagesWithoutPings}
                  setMessages={setMessages}
                  append={append}
                />
              )}
            </form>
          </>
        )}
      </div>

      <Artifact
        chatId={id}
        input={input}
        setInput={setInput}
        handleSubmit={handleSubmit}
        status={status}
        stop={stop}
        attachments={attachments}
        setAttachments={setAttachments}
        append={append}
        messages={messagesWithoutPings}
        setMessages={setMessages}
        reload={reload}
        votes={votes}
        isReadonly={isReadonly}
      />
    </>
  );
}
