import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import { auth } from '../(auth)/auth';
import { Chat } from '@/components/chat';
import { DEFAULT_CHAT_MODEL } from '@/lib/ai/models';
import { generateUUID } from '@/lib/utils';
import { DataStreamHandler } from '@/components/data-stream-handler';

export default async function Page() {
  const session = await auth();

  if (!session) {
    redirect('/api/auth/guest');
  }

  const id = generateUUID();
  const cookieStore = await cookies();
  const chatModelFromCookie = cookieStore.get('chat-model');

  // Déterminer si l'utilisateur est propriétaire (toujours true pour un nouveau chat)
  const isOwner = true;

  // Configuration commune pour le nouveau chat
  const chatProps = {
    key: id,
    id,
    initialMessages: [],
    initialVisibilityType: 'private' as const,
    isReadonly: !isOwner,
    session,
    autoResume: true, // ✅ Activé pour une meilleure UX
  };

  return (
    <>
      <Chat
        {...chatProps}
        initialChatModel={chatModelFromCookie?.value || DEFAULT_CHAT_MODEL}
      />
      <DataStreamHandler id={id} />
    </>
  );
}
