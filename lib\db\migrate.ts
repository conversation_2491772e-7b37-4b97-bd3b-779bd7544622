import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';
import { config } from 'dotenv';

config();

// Your existing code

const runMigrate = async () => {
  try {
    if (!process.env.POSTGRES_URL) {
      throw new Error('POSTGRES_URL is not defined');
    }

    console.log('Connecting to database with URL:', process.env.POSTGRES_URL);

    const connection = postgres(process.env.POSTGRES_URL, { 
      max: 1,
      ssl: process.env.POSTGRES_URL.includes('supabase') 
        ? { rejectUnauthorized: false } 
        : false
    });
    const db = drizzle(connection);

    console.log('⏳ Running migrations...');

    const start = Date.now();
    await migrate(db, { migrationsFolder: './lib/db/migrations' });
    const end = Date.now();

    console.log('✅ Migrations completed in', end - start, 'ms');
    process.exit(0);
  } catch (err) {
    console.error('❌ Migration failed');
    console.error(err);
    process.exit(1);
  }
};

runMigrate();
