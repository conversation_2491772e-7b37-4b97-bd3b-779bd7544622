import 'server-only';

import { generateHashedPassword } from './utils';
import {
  and,
  asc,
  count,
  desc,
  eq,
  gt,
  gte,
  inArray,
  lt,
  type SQL,
} from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

import {
  user,
  chat,
  type User,
  document,
  type Suggestion,
  suggestion,
  message,
  vote,
  stream,
  type DBMessage,
  type Chat,
} from './schema';
import type { ArtifactKind } from '@/components/artifact';
import type { VisibilityType } from '@/components/visibility-selector';
import { generateUUID } from '../utils';
import { ChatSDKError } from '../errors';
import {
  getCachedChatsByUserId,
  cacheChatsByUserId,
  invalidateUserChatsCache,
  getCachedMessageCount,
  cacheMessageCount,
  invalidateMessageCountCache,
  getCachedMessagesByChatId,
  cacheMessagesByChatId,
  invalidateMessagesByChatIdCache,
} from '@/lib/cache';

// Optionally, if not using email/pass login, you can
// use the Drizzle adapter for Auth.js / NextAuth
// https://authjs.dev/reference/adapter/drizzle

// biome-ignore lint: Forbidden non-null assertion.
const client = postgres(process.env.POSTGRES_URL!);
const db = drizzle(client);

export async function getUser(email: string): Promise<Array<User>> {
  try {
    return await db.select().from(user).where(eq(user.email, email));
  } catch (error) {
    console.error('Failed to get user from database');
    // Wrap in ChatSDKError but preserve original behavior
    if (error instanceof ChatSDKError) {
      throw error;
    }
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get user by email',
    );
  }
}

export async function createUser(email: string, password: string) {
  const hashedPassword = generateHashedPassword(password);

  try {
    return await db.insert(user).values({ email, password: hashedPassword });
  } catch (error) {
    console.error('Failed to create user in database');
    // Wrap in ChatSDKError but preserve original behavior
    if (error instanceof ChatSDKError) {
      throw error;
    }
    throw new ChatSDKError('bad_request:database', 'Failed to create user');
  }
}

export async function createGuestUser() {
  const email = `guest-${Date.now()}`;
  const password = generateHashedPassword(generateUUID());

  try {
    return await db.insert(user).values({ email, password }).returning({
      id: user.id,
      email: user.email,
    });
  } catch (error) {
    console.error('Failed to create guest user in database');
    // Wrap in ChatSDKError but preserve original behavior
    if (error instanceof ChatSDKError) {
      throw error;
    }
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to create guest user',
    );
  }
}

export async function saveChat({
  id,
  userId,
  title,
  visibility,
}: {
  id: string;
  userId: string;
  title: string;
  visibility?: VisibilityType;
}) {
  try {
    const [savedChat] = await db
      .insert(chat)
      .values({
        id,
        createdAt: new Date(),
        userId,
        title,
        visibility,
      })
      .returning();

    return savedChat;
  } catch (error) {
    // Log pour debugging en développement
    if (process.env.NODE_ENV === 'development') {
      console.error('Failed to save chat:', error);
    }
    throw new ChatSDKError('bad_request:database', 'Failed to save chat');
  }
}

export async function deleteChatById({ id }: { id: string }) {
  try {
    // Récupérer l'userId pour invalider le cache après suppression
    const [chatToDelete] = await db
      .select({ userId: chat.userId })
      .from(chat)
      .where(eq(chat.id, id));

    if (!chatToDelete) return null;

    const deleted = await db.transaction(async (tx) => {
      // Supprimer toutes les données liées au chat
      await tx.delete(vote).where(eq(vote.chatId, id));
      await tx.delete(message).where(eq(message.chatId, id));
      await tx.delete(stream).where(eq(stream.chatId, id));

      const [deletedChat] = await tx
        .delete(chat)
        .where(eq(chat.id, id))
        .returning();

      return deletedChat;
    });

    if (chatToDelete.userId) {
      invalidateUserChatsCache(chatToDelete.userId);
    }

    return deleted;
  } catch (error) {
    // Log pour debugging en développement
    if (process.env.NODE_ENV === 'development') {
      console.error('Failed to delete chat by id:', error);
    }
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to delete chat by id',
    );
  }
}

export async function createStreamId({
  streamId,
  chatId,
}: {
  streamId: string;
  chatId: string;
}) {
  try {
    const [createdStream] = await db
      .insert(stream)
      .values({
        id: streamId,
        chatId,
        createdAt: new Date(),
        status: 'active',
        lastActivity: new Date(),
      })
      .returning();

    return createdStream;
  } catch (error) {
    // Log pour debugging en développement
    if (process.env.NODE_ENV === 'development') {
      console.error('Failed to create stream id:', error);
    }
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to create stream id',
    );
  }
}

export async function getStreamIdsByChatId({ chatId }: { chatId: string }) {
  try {
    const streamIds = await db
      .select({
        id: stream.id,
        status: stream.status,
        createdAt: stream.createdAt,
        lastActivity: stream.lastActivity,
      })
      .from(stream)
      .where(eq(stream.chatId, chatId))
      .orderBy(asc(stream.createdAt));

    return streamIds;
  } catch (error) {
    // Log pour debugging en développement
    if (process.env.NODE_ENV === 'development') {
      console.error('Failed to get stream ids by chat id:', error);
    }
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get stream ids by chat id',
    );
  }
}

export async function updateStreamStatus({
  streamId,
  status,
}: {
  streamId: string;
  status: 'active' | 'completed' | 'error' | 'cancelled';
}) {
  try {
    const [updatedStream] = await db
      .update(stream)
      .set({
        status,
        lastActivity: new Date(),
      })
      .where(eq(stream.id, streamId))
      .returning();

    return updatedStream;
  } catch (error) {
    // Log pour debugging en développement
    if (process.env.NODE_ENV === 'development') {
      console.error('Failed to update stream status:', error);
    }
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to update stream status',
    );
  }
}

export async function getChatsByUserId({
  id,
  limit,
  startingAfter,
  endingBefore,
}: {
  id: string;
  limit: number;
  startingAfter: string | null;
  endingBefore: string | null;
}) {
  try {
    // Vérifier si les résultats sont dans le cache
    const cachedResult = getCachedChatsByUserId(
      id,
      limit,
      startingAfter,
      endingBefore,
    );
    if (cachedResult) {
      return cachedResult;
    }

    const extendedLimit = limit + 1;

    // Optimisation: Utiliser des requêtes préparées pour de meilleures performances
    const query = (whereCondition?: SQL<any>) =>
      db
        .select()
        .from(chat)
        .where(
          whereCondition
            ? and(whereCondition, eq(chat.userId, id))
            : eq(chat.userId, id),
        )
        .orderBy(desc(chat.createdAt))
        .limit(extendedLimit);

    let filteredChats: Array<Chat> = [];

    if (startingAfter) {
      // Optimisation: Utiliser une seule requête avec une sous-requête
      filteredChats = await db
        .select()
        .from(chat)
        .where(
          and(
            eq(chat.userId, id),
            gt(
              chat.createdAt,
              db
                .select({ createdAt: chat.createdAt })
                .from(chat)
                .where(eq(chat.id, startingAfter))
                .limit(1),
            ),
          ),
        )
        .orderBy(desc(chat.createdAt))
        .limit(extendedLimit);
    } else if (endingBefore) {
      // Optimisation: Utiliser une seule requête avec une sous-requête
      filteredChats = await db
        .select()
        .from(chat)
        .where(
          and(
            eq(chat.userId, id),
            lt(
              chat.createdAt,
              db
                .select({ createdAt: chat.createdAt })
                .from(chat)
                .where(eq(chat.id, endingBefore))
                .limit(1),
            ),
          ),
        )
        .orderBy(desc(chat.createdAt))
        .limit(extendedLimit);
    } else {
      filteredChats = await query();
    }

    const hasMore = filteredChats.length > limit;
    const result = {
      chats: hasMore ? filteredChats.slice(0, limit) : filteredChats,
      hasMore,
    };

    // Mettre en cache les résultats
    cacheChatsByUserId(id, limit, startingAfter, endingBefore, result);

    return result;
  } catch (error) {
    console.error('Failed to get chats by user from database', error);
    // Wrap in ChatSDKError but preserve original behavior
    if (error instanceof ChatSDKError) {
      throw error;
    }
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get chats by user id',
    );
  }
}

export async function getChatById({ id }: { id: string }) {
  try {
    const [selectedChat] = await db.select().from(chat).where(eq(chat.id, id));
    return selectedChat;
  } catch (error) {
    console.error('Failed to get chat by id from database');
    // Wrap in ChatSDKError but preserve original behavior
    if (error instanceof ChatSDKError) {
      throw error;
    }
    throw new ChatSDKError('bad_request:database', 'Failed to get chat by id');
  }
}

export async function saveMessages({
  messages,
}: {
  messages: Array<DBMessage>;
}) {
  try {
    // Insérer les messages
    const result = await db.insert(message).values(messages);

    // Récupérer les chatIds uniques
    const chatIds = [...new Set(messages.map((msg) => msg.chatId))];

    for (const chatId of chatIds) {
      // Invalider le cache des messages pour ce chat
      invalidateMessagesByChatIdCache(chatId);

      // Si des messages utilisateur ont été ajoutés, invalider le cache du nombre de messages
      if (
        messages.some((msg) => msg.role === 'user' && msg.chatId === chatId)
      ) {
        const [chatInfo] = await db
          .select({ userId: chat.userId })
          .from(chat)
          .where(eq(chat.id, chatId));

        if (chatInfo?.userId) {
          invalidateMessageCountCache(chatInfo.userId);
        }
      }
    }

    return result;
  } catch (error) {
    console.error('Failed to save messages in database', error);
    // Wrap in ChatSDKError but preserve original behavior
    if (error instanceof ChatSDKError) {
      throw error;
    }
    throw new ChatSDKError('bad_request:database', 'Failed to save messages');
  }
}

export async function getMessagesByChatId({ id }: { id: string }) {
  try {
    // Vérifier si les résultats sont dans le cache
    const cachedMessages = getCachedMessagesByChatId(id);
    if (cachedMessages) {
      return cachedMessages;
    }

    // Récupérer les messages depuis la base de données
    const messages = await db
      .select()
      .from(message)
      .where(eq(message.chatId, id))
      .orderBy(asc(message.createdAt));

    // Mettre en cache les résultats
    cacheMessagesByChatId(id, messages);

    return messages;
  } catch (error) {
    console.error('Failed to get messages by chat id from database', error);
    // Wrap in ChatSDKError but preserve original behavior
    if (error instanceof ChatSDKError) {
      throw error;
    }
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get messages by chat id',
    );
  }
}

export async function voteMessage({
  chatId,
  messageId,
  type,
}: {
  chatId: string;
  messageId: string;
  type: 'up' | 'down';
}) {
  try {
    const [existingVote] = await db
      .select()
      .from(vote)
      .where(and(eq(vote.messageId, messageId)));

    if (existingVote) {
      return await db
        .update(vote)
        .set({ isUpvoted: type === 'up' })
        .where(and(eq(vote.messageId, messageId), eq(vote.chatId, chatId)));
    }
    return await db.insert(vote).values({
      chatId,
      messageId,
      isUpvoted: type === 'up',
    });
  } catch (error) {
    console.error('Failed to upvote message in database', error);
    // Wrap in ChatSDKError but preserve original behavior
    if (error instanceof ChatSDKError) {
      throw error;
    }
    throw new ChatSDKError('bad_request:database', 'Failed to vote message');
  }
}

export async function getVotesByChatId({ id }: { id: string }) {
  try {
    return await db.select().from(vote).where(eq(vote.chatId, id));
  } catch (error) {
    console.error('Failed to get votes by chat id from database', error);
    // Wrap in ChatSDKError but preserve original behavior
    if (error instanceof ChatSDKError) {
      throw error;
    }
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get votes by chat id',
    );
  }
}

export async function saveDocument({
  id,
  title,
  kind,
  content,
  userId,
}: {
  id: string;
  title: string;
  kind: ArtifactKind;
  content: string;
  userId: string;
}) {
  try {
    console.log(`Saving document with kind: ${kind}`, { id, title, kind });

    // Vérifier si le contenu est un JSON valide pour un artifact HTML
    let isHtmlArtifact = false;
    if (content) {
      try {
        const parsedContent = JSON.parse(content);
        if (
          parsedContent &&
          typeof parsedContent === 'object' &&
          (parsedContent.htmlContent ||
            parsedContent.cssContent ||
            parsedContent.jsContent)
        ) {
          isHtmlArtifact = true;
          console.log(`saveDocument - Valid HTML artifact detected for ${id}`);
        }
      } catch (e) {
        // Pas un JSON valide, donc pas un artifact HTML
      }
    }

    // Vérifier que la valeur de kind est l'une des valeurs autorisées pour la colonne 'text'
    // Si ce n'est pas le cas, utiliser 'text' comme valeur par défaut
    let dbTextValue: 'text' | 'code' | 'image' | 'sheet' | 'html' = 'text';

    // Vérifier si kind est une valeur valide pour la colonne 'text'
    if (['text', 'code', 'image', 'sheet', 'html'].includes(kind)) {
      dbTextValue = kind as 'text' | 'code' | 'image' | 'sheet' | 'html';
    } else {
      console.log(
        `saveDocument - Invalid kind value: ${kind}, using 'text' as default`,
      );
    }

    // Pour les artifacts HTML, s'assurer que la valeur est 'html'
    if (isHtmlArtifact) {
      dbTextValue = 'html';
      console.log(
        `saveDocument - HTML artifact detected, using 'html' in the database column`,
      );
    }

    // Créer un objet avec les propriétés correctes pour l'insertion
    const insertValues = {
      id,
      title,
      content,
      userId,
      createdAt: new Date(),
      text: dbTextValue, // La colonne dans la base de données
    };

    console.log(
      `saveDocument - Using text=${insertValues.text} (original=${kind}, isHtmlArtifact=${isHtmlArtifact})`,
    );

    console.log('Insert values:', insertValues);

    const result = await db.insert(document).values(insertValues).returning();
    console.log('Document saved with result:', result);

    return result;
  } catch (error) {
    console.error(`Failed to save document in database (kind: ${kind})`, error);
    // Wrap in ChatSDKError but preserve original behavior
    if (error instanceof ChatSDKError) {
      throw error;
    }
    throw new ChatSDKError('bad_request:database', 'Failed to save document');
  }
}

export async function getDocumentsById({ id }: { id: string }) {
  // Special handling for 'init' ID which is used as a placeholder
  if (id === 'init') {
    console.log(
      'getDocumentsById called with placeholder ID "init", returning empty array',
    );
    return [];
  }

  try {
    // Validate that the ID is a valid UUID before querying the database
    if (
      !id.match(
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
      )
    ) {
      console.error(`Invalid UUID format for document ID: ${id}`);
      return [];
    }

    const rawDocuments = await db
      .select()
      .from(document)
      .where(eq(document.id, id))
      .orderBy(asc(document.createdAt));

    console.log('Raw documents from database:', rawDocuments);

    // Transformer les documents pour utiliser 'kind' au lieu de 'text'
    const documents = rawDocuments.map((doc) => {
      // Vérifier si le contenu est un JSON valide pour un artifact HTML
      let isHtmlArtifact = false;
      let parsedContent = null;

      if (doc.content) {
        // Vérifier d'abord si le text est déjà 'html'
        if (doc.text === 'html') {
          isHtmlArtifact = true;
          console.log(
            `DB - Document ${doc.id}: Already marked as HTML artifact`,
          );
        } else {
          // Vérifier si le contenu ressemble à du JSON (commence par { ou [)
          const contentTrimmed = doc.content.trim();
          if (
            contentTrimmed.startsWith('{') ||
            contentTrimmed.startsWith('[')
          ) {
            try {
              parsedContent = JSON.parse(doc.content);
              // Vérifier si au moins un des champs HTML est présent
              if (
                parsedContent &&
                typeof parsedContent === 'object' &&
                (parsedContent.htmlContent ||
                  parsedContent.cssContent ||
                  parsedContent.jsContent)
              ) {
                isHtmlArtifact = true;
                console.log(
                  `DB - Document ${doc.id}: Valid HTML artifact detected`,
                  {
                    hasHtml: !!parsedContent.htmlContent,
                    hasCss: !!parsedContent.cssContent,
                    hasJs: !!parsedContent.jsContent,
                  },
                );
              }
            } catch (e) {
              console.warn(
                `DB - Document ${doc.id}: Failed to parse content as JSON`,
                e,
              );
              // Pas un JSON valide, donc pas un artifact HTML
            }
          } else {
            // Le contenu ne ressemble pas à du JSON, probablement du texte brut
            console.log(
              `DB - Document ${doc.id}: Content appears to be plain text, not JSON`,
            );
          }
        }
      }

      // Déterminer la valeur de 'kind' à retourner
      // Si c'est un artifact HTML détecté par le contenu, utiliser 'html'
      // Sinon, utiliser la valeur de 'text' de la base de données
      let kind = doc.text;

      // Si c'est un artifact HTML, toujours utiliser 'html' comme kind
      if (isHtmlArtifact) {
        kind = 'html';
      }

      console.log(
        `Document ${doc.id}: Using kind=${kind}, original text=${doc.text}, isHtmlArtifact=${isHtmlArtifact}`,
      );

      // Créer un nouvel objet avec la propriété 'kind' au lieu de 'text'
      const { text, ...rest } = doc;
      return {
        ...rest,
        kind,
      };
    });

    return documents;
  } catch (error) {
    console.error(`Failed to get document by id from database: ${id}`, error);
    // Return an empty array instead of throwing to prevent cascading errors
    return [];
  }
}

export async function getDocumentById({ id }: { id: string }) {
  // Special handling for 'init' ID which is used as a placeholder
  if (id === 'init') {
    console.log(
      'getDocumentById called with placeholder ID "init", returning null',
    );
    return null;
  }

  try {
    // Validate that the ID is a valid UUID before querying the database
    if (
      !id.match(
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
      )
    ) {
      console.error(`Invalid UUID format for document ID: ${id}`);
      return null;
    }

    const [rawDocument] = await db
      .select()
      .from(document)
      .where(eq(document.id, id))
      .orderBy(desc(document.createdAt));

    if (!rawDocument) {
      return null;
    }

    // Vérifier si le contenu est un JSON valide pour un artifact HTML
    let isHtmlArtifact = false;
    let parsedContent = null;

    if (rawDocument.content) {
      // Vérifier d'abord si le text est déjà 'html'
      if (rawDocument.text === 'html') {
        isHtmlArtifact = true;
        console.log(
          `DB - Document ${rawDocument.id}: Already marked as HTML artifact`,
        );
      } else {
        // Vérifier si le contenu ressemble à du JSON (commence par { ou [)
        const contentTrimmed = rawDocument.content.trim();
        if (contentTrimmed.startsWith('{') || contentTrimmed.startsWith('[')) {
          try {
            parsedContent = JSON.parse(rawDocument.content);
            // Vérifier si au moins un des champs HTML est présent
            if (
              parsedContent &&
              typeof parsedContent === 'object' &&
              (parsedContent.htmlContent ||
                parsedContent.cssContent ||
                parsedContent.jsContent)
            ) {
              isHtmlArtifact = true;
              console.log(
                `DB - Document ${rawDocument.id}: Valid HTML artifact detected`,
                {
                  hasHtml: !!parsedContent.htmlContent,
                  hasCss: !!parsedContent.cssContent,
                  hasJs: !!parsedContent.jsContent,
                },
              );
            }
          } catch (e) {
            console.warn(
              `DB - Document ${rawDocument.id}: Failed to parse content as JSON`,
              e,
            );
            // Pas un JSON valide, donc pas un artifact HTML
          }
        } else {
          // Le contenu ne ressemble pas à du JSON, probablement du texte brut
          console.log(
            `DB - Document ${rawDocument.id}: Content appears to be plain text, not JSON`,
          );
        }
      }
    }

    // Déterminer la valeur de 'kind' à retourner
    // Si c'est un artifact HTML détecté par le contenu, utiliser 'html'
    // Sinon, utiliser la valeur de 'text' de la base de données
    let kind = rawDocument.text;

    // Si c'est un artifact HTML, toujours utiliser 'html' comme kind
    if (isHtmlArtifact) {
      kind = 'html';
    }

    console.log(
      `Document ${rawDocument.id}: Using kind=${kind}, original text=${rawDocument.text}, isHtmlArtifact=${isHtmlArtifact}`,
    );

    // Créer un nouvel objet avec la propriété 'kind' au lieu de 'text'
    const { text, ...rest } = rawDocument;
    const selectedDocument = {
      ...rest,
      kind,
    };

    return selectedDocument;
  } catch (error) {
    console.error(`Failed to get document by id from database: ${id}`, error);
    return null;
  }
}

export async function deleteDocumentsByIdAfterTimestamp({
  id,
  timestamp,
}: {
  id: string;
  timestamp: Date;
}) {
  // Special handling for 'init' ID which is used as a placeholder
  if (id === 'init') {
    console.log(
      'deleteDocumentsByIdAfterTimestamp called with placeholder ID "init", returning empty array',
    );
    return [];
  }

  try {
    // Validate that the ID is a valid UUID before querying the database
    if (
      !id.match(
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
      )
    ) {
      console.error(`Invalid UUID format for document ID: ${id}`);
      return [];
    }

    // Log pour le débogage
    console.log(
      `Deleting document with id ${id} and timestamp ${timestamp.toISOString()}`,
    );

    // Supprimer d'abord les suggestions associées
    await db.delete(suggestion).where(
      and(
        eq(suggestion.documentId, id),
        gte(suggestion.documentCreatedAt, timestamp), // Utiliser gte au lieu de gt
      ),
    );

    // Vérifier si le document existe avant de le supprimer
    const documentsToDelete = await db
      .select()
      .from(document)
      .where(and(eq(document.id, id), gte(document.createdAt, timestamp)));

    console.log(`Found ${documentsToDelete.length} documents to delete`);

    if (documentsToDelete.length === 0) {
      // Si aucun document n'est trouvé avec gte, essayer avec eq (égalité exacte)
      console.log('Trying with exact timestamp match');
      const exactDocuments = await db
        .select()
        .from(document)
        .where(and(eq(document.id, id), eq(document.createdAt, timestamp)));

      console.log(
        `Found ${exactDocuments.length} documents with exact timestamp match`,
      );

      if (exactDocuments.length > 0) {
        // Supprimer les documents avec une correspondance exacte
        const deletedDocuments = await db
          .delete(document)
          .where(and(eq(document.id, id), eq(document.createdAt, timestamp)))
          .returning();

        return deletedDocuments;
      }
    }

    // Supprimer le document avec gte
    const deletedDocuments = await db
      .delete(document)
      .where(and(eq(document.id, id), gte(document.createdAt, timestamp)))
      .returning();

    // Log pour le débogage
    console.log(`Deleted ${deletedDocuments.length} documents`);

    return deletedDocuments;
  } catch (error) {
    console.error(
      `Failed to delete documents by id after timestamp from database: ${id}`,
      error,
    );
    // Return an empty array instead of throwing to prevent cascading errors
    return [];
  }
}

export async function saveSuggestions({
  suggestions,
}: {
  suggestions: Array<Suggestion>;
}) {
  try {
    return await db.insert(suggestion).values(suggestions);
  } catch (error) {
    console.error('Failed to save suggestions in database');
    // Wrap in ChatSDKError but preserve original behavior
    if (error instanceof ChatSDKError) {
      throw error;
    }
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to save suggestions',
    );
  }
}

export async function getSuggestionsByDocumentId({
  documentId,
}: {
  documentId: string;
}) {
  // Special handling for 'init' ID which is used as a placeholder
  if (documentId === 'init') {
    console.log(
      'getSuggestionsByDocumentId called with placeholder ID "init", returning empty array',
    );
    return [];
  }

  try {
    // Validate that the ID is a valid UUID before querying the database
    if (
      !documentId.match(
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
      )
    ) {
      console.error(`Invalid UUID format for document ID: ${documentId}`);
      return [];
    }

    return await db
      .select()
      .from(suggestion)
      .where(and(eq(suggestion.documentId, documentId)));
  } catch (error) {
    console.error(
      `Failed to get suggestions by document version from database: ${documentId}`,
      error,
    );
    // Return an empty array instead of throwing to prevent cascading errors
    return [];
  }
}

export async function getMessageById({ id }: { id: string }) {
  try {
    return await db.select().from(message).where(eq(message.id, id));
  } catch (error) {
    console.error('Failed to get message by id from database');
    // Wrap in ChatSDKError but preserve original behavior
    if (error instanceof ChatSDKError) {
      throw error;
    }
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get message by id',
    );
  }
}

export async function deleteMessagesByChatIdAfterTimestamp({
  chatId,
  timestamp,
}: {
  chatId: string;
  timestamp: Date;
}) {
  try {
    const messagesToDelete = await db
      .select({ id: message.id })
      .from(message)
      .where(
        and(eq(message.chatId, chatId), gte(message.createdAt, timestamp)),
      );

    const messageIds = messagesToDelete.map((message) => message.id);

    if (messageIds.length > 0) {
      await db
        .delete(vote)
        .where(
          and(eq(vote.chatId, chatId), inArray(vote.messageId, messageIds)),
        );

      return await db
        .delete(message)
        .where(
          and(eq(message.chatId, chatId), inArray(message.id, messageIds)),
        );
    }
  } catch (error) {
    console.error(
      'Failed to delete messages by id after timestamp from database',
    );
    // Wrap in ChatSDKError but preserve original behavior
    if (error instanceof ChatSDKError) {
      throw error;
    }
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to delete messages by chat id after timestamp',
    );
  }
}

export async function updateChatVisibilityById({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: VisibilityType;
}) {
  try {
    const [updatedChat] = await db
      .update(chat)
      .set({ visibility })
      .where(eq(chat.id, chatId))
      .returning();

    return updatedChat;
  } catch (error) {
    // Log pour debugging en développement
    if (process.env.NODE_ENV === 'development') {
      console.error('Failed to update chat visibility:', error);
    }
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to update chat visibility by id',
    );
  }
}

export async function getChatVisibilityById({ chatId }: { chatId: string }) {
  try {
    const [selectedChat] = await db
      .select({ visibility: chat.visibility })
      .from(chat)
      .where(eq(chat.id, chatId));

    return selectedChat?.visibility || 'private';
  } catch (error) {
    // Log pour debugging en développement
    if (process.env.NODE_ENV === 'development') {
      console.error('Failed to get chat visibility:', error);
    }
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get chat visibility by id',
    );
  }
}

// Fonction utilitaire pour valider la visibilité
export function validateVisibility(visibility: string): VisibilityType {
  if (visibility === 'public' || visibility === 'private') {
    return visibility;
  }
  return 'private'; // Valeur par défaut sécurisée
}

// Fonction pour récupérer les chats publics (pour une page publique)
export async function getPublicChats({
  limit = 10,
  offset = 0,
}: {
  limit?: number;
  offset?: number;
} = {}) {
  try {
    const publicChats = await db
      .select({
        id: chat.id,
        title: chat.title,
        createdAt: chat.createdAt,
        userId: chat.userId,
      })
      .from(chat)
      .where(eq(chat.visibility, 'public'))
      .orderBy(desc(chat.createdAt))
      .limit(limit)
      .offset(offset);

    return publicChats;
  } catch (error) {
    // Log pour debugging en développement
    if (process.env.NODE_ENV === 'development') {
      console.error('Failed to get public chats:', error);
    }
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get public chats',
    );
  }
}

// Ajoutez cette fonction pour créer un utilisateur Google s'il n'existe pas
export async function getOrCreateGoogleUser(email: string) {
  try {
    const users = await db.select().from(user).where(eq(user.email, email));

    if (users.length > 0) {
      return users[0];
    }

    // Créer un nouvel utilisateur pour Google (sans mot de passe)
    const [newUser] = await db
      .insert(user)
      .values({
        email,
        // Pas de mot de passe pour les utilisateurs Google
      })
      .returning();

    return newUser;
  } catch (error) {
    // Wrap in ChatSDKError but preserve original behavior
    if (error instanceof ChatSDKError) {
      throw error;
    }
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get or create Google user',
    );
  }
}

export async function getMessageCountByUserId({
  id,
  differenceInHours,
}: { id: string; differenceInHours: number }) {
  try {
    // Vérifier si le résultat est dans le cache
    const cachedCount = getCachedMessageCount(id, differenceInHours);
    if (cachedCount !== undefined) {
      return cachedCount;
    }

    const twentyFourHoursAgo = new Date(
      Date.now() - differenceInHours * 60 * 60 * 1000,
    );

    // Optimisation: Utiliser un index pour accélérer la requête
    const [stats] = await db
      .select({ count: count(message.id) })
      .from(message)
      .innerJoin(chat, eq(message.chatId, chat.id))
      .where(
        and(
          eq(chat.userId, id),
          gte(message.createdAt, twentyFourHoursAgo),
          eq(message.role, 'user'),
        ),
      )
      .execute();

    const messageCount = stats?.count ?? 0;

    // Mettre en cache le résultat
    cacheMessageCount(id, differenceInHours, messageCount);

    return messageCount;
  } catch (error) {
    console.error(
      'Failed to get message count by user id for the last 24 hours from database',
      error,
    );
    // Wrap in ChatSDKError but preserve original behavior
    if (error instanceof ChatSDKError) {
      throw error;
    }
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get message count by user id',
    );
  }
}
