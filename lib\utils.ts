import type {
  CoreAssistantMessage,
  CoreToolMessage,
  UIMessage,
  UIMessagePart,
} from 'ai';
import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { formatISO } from 'date-fns';

import type { Document, DBMessage } from './db/schema';
import type {
  Place,
  PlaceImage,
  SearchResult,
  ChatMessage,
  ChatTools,
  CustomUIDataTypes,
} from './types';
import { ChatSDKError, type ErrorCode } from './errors';

import {
  Globe,
  Book,
  TelescopeIcon,
  DollarSign,
  MessageCircle as ChatsCircleIcon,
  Code as CodeIcon,
  Brain as MemoryIcon,
  SquareCode as RedditLogoIcon,
  Youtube as YoutubeLogoIcon,
  Twitter as XLogoIcon,
} from 'lucide-react';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const fetcher = async (url: string) => {
  const response = await fetch(url);

  if (!response.ok) {
    // Try to get error details in the new format first
    let errorData: {
      code?: ErrorCode;
      cause?: string;
      message?: string;
      error?: string;
    };
    try {
      errorData = await response.json();
    } catch {
      errorData = { message: 'An error occurred while fetching the data.' };
    }

    // If server returns the new format { code, cause }, use it directly
    if (errorData.code && errorData.cause) {
      throw new ChatSDKError(errorData.code, errorData.cause);
    }

    // Fallback: Map HTTP status codes to ChatSDKError types (backward compatibility)
    let errorCode: ErrorCode;
    if (response.status === 401) {
      errorCode = 'unauthorized:api';
    } else if (response.status === 403) {
      errorCode = 'forbidden:api';
    } else if (response.status === 404) {
      errorCode = 'not_found:api';
    } else if (response.status === 429) {
      errorCode = 'rate_limit:api';
    } else {
      errorCode = 'bad_request:api';
    }

    // Create ChatSDKError with appropriate message
    const errorMessage =
      errorData.message ||
      errorData.error ||
      'An error occurred while fetching the data.';
    throw new ChatSDKError(errorCode, errorMessage);
  }

  return response.json();
};

export async function fetchWithErrorHandlers(
  input: RequestInfo | URL,
  init?: RequestInit,
) {
  try {
    const response = await fetch(input, init);

    if (!response.ok) {
      // Try to get error details in the new format first
      let errorData: {
        code?: ErrorCode;
        cause?: string;
        message?: string;
        error?: string;
      };
      try {
        errorData = await response.json();
      } catch {
        errorData = { message: 'An error occurred while fetching the data.' };
      }

      // If server returns the new format { code, cause }, use it directly
      if (errorData.code && errorData.cause) {
        throw new ChatSDKError(errorData.code, errorData.cause);
      }

      // Fallback: Map HTTP status codes to ChatSDKError types (backward compatibility)
      let errorCode: ErrorCode;
      if (response.status === 401) {
        errorCode = 'unauthorized:api';
      } else if (response.status === 403) {
        errorCode = 'forbidden:api';
      } else if (response.status === 404) {
        errorCode = 'not_found:api';
      } else if (response.status === 429) {
        errorCode = 'rate_limit:api';
      } else {
        errorCode = 'bad_request:api';
      }

      // Create ChatSDKError with appropriate message
      const errorMessage =
        errorData.message ||
        errorData.error ||
        'An error occurred while fetching the data.';
      throw new ChatSDKError(errorCode, errorMessage);
    }

    return response;
  } catch (error: unknown) {
    // Check for offline status
    if (typeof navigator !== 'undefined' && !navigator.onLine) {
      throw new ChatSDKError(
        'offline:api',
        'You appear to be offline. Please check your internet connection.',
      );
    }

    // Re-throw ChatSDKError as-is
    if (error instanceof ChatSDKError) {
      throw error;
    }

    // Re-throw other errors
    throw error;
  }
}

export function getLocalStorage(key: string) {
  if (typeof window !== 'undefined') {
    return JSON.parse(localStorage.getItem(key) || '[]');
  }
  return [];
}

export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

type ResponseMessageWithoutId = CoreToolMessage | CoreAssistantMessage;
type ResponseMessage = ResponseMessageWithoutId & { id: string };

export function getMostRecentUserMessage(messages: Array<UIMessage>) {
  const userMessages = messages.filter((message) => message.role === 'user');
  return userMessages.at(-1);
}

export function getDocumentTimestampByIndex(
  documents: Array<Document>,
  index: number,
) {
  if (!documents) return new Date();
  if (index > documents.length) return new Date();

  return documents[index].createdAt;
}

export function getTrailingMessageId({
  messages,
}: {
  messages: Array<ResponseMessage>;
}): string | null {
  const trailingMessage = messages.at(-1);

  if (!trailingMessage) return null;

  return trailingMessage.id;
}

export function getUserId(): string {
  // Si vous avez un système d'authentification, récupérez l'ID de l'utilisateur
  // Sinon, utilisez un ID stocké dans localStorage ou générez-en un
  const storedId =
    typeof window !== 'undefined' ? localStorage.getItem('user_id') : null;

  if (storedId) {
    return storedId;
  }

  // Générer un ID aléatoire si aucun n'existe
  const newId = generateUUID();
  if (typeof window !== 'undefined') {
    localStorage.setItem('user_id', newId);
  }

  return newId;
}

export function sanitizeText(text: string): string {
  return text.replace('<has_function_call>', '');
}

export async function serperSearch(
  message: string,
  numberOfPagesToScan = 5,
): Promise<SearchResult[]> {
  const url = 'https://google.serper.dev/search';
  const data = JSON.stringify({
    q: message,
  });
  const requestOptions: RequestInit = {
    method: 'POST',
    headers: {
      'X-API-KEY': process.env.SERPER_API as string,
      'Content-Type': 'application/json',
    },
    body: data,
  };
  try {
    const response = await fetch(url, requestOptions);
    if (!response.ok) {
      throw new Error(
        `Network response was not ok. Status: ${response.status}`,
      );
    }
    const responseData = await response.json();
    if (!responseData.organic) {
      throw new Error('Invalid API response format');
    }
    const final = responseData.organic.map(
      (result: any): SearchResult => ({
        title: result.title,
        link: result.link,
        favicon: result.favicons?.[0] || '',
      }),
    );
    return final;
  } catch (error) {
    console.error('Error fetching search results:', error);
    throw error;
  }
}

/**
 * Récupère des images pour un lieu spécifique via Serper Images API
 */
async function serperImageSearch(
  placeName: string,
  placeAddress?: string,
): Promise<PlaceImage[]> {
  // Images de fallback pour les lieux japonais populaires
  const fallbackImages: { [key: string]: PlaceImage[] } = {
    'godzilla head': [
      {
        url: 'https://images.pexels.com/photos/2070033/pexels-photo-2070033.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
        description: 'Godzilla Head in Shinjuku',
        thumbnail:
          'https://images.pexels.com/photos/2070033/pexels-photo-2070033.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
      },
    ],
    'tokyo national museum': [
      {
        url: 'https://images.pexels.com/photos/2506923/pexels-photo-2506923.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
        description: 'Tokyo National Museum',
        thumbnail:
          'https://images.pexels.com/photos/2506923/pexels-photo-2506923.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
      },
    ],
    'shibuya nonbei yokocho': [
      {
        url: 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
        description: 'Shibuya Nonbei Yokocho',
        thumbnail:
          'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
      },
    ],
  };

  try {
    const apiKey = process.env.SERPER_API as string;

    // Construire la requête de recherche d'images
    const searchQuery = placeAddress
      ? `${placeName} ${placeAddress}`
      : placeName;

    const url = `https://google.serper.dev/images?q=${encodeURIComponent(searchQuery)}`;
    console.log(`Searching images for: ${searchQuery}`);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-API-KEY': apiKey,
        'Content-Type': 'application/json',
      },
      redirect: 'follow',
    });

    if (!response.ok) {
      console.error(`Image search failed for ${placeName}: ${response.status}`);
      throw new Error(`HTTP ${response.status}`);
    }

    const data = await response.json();
    console.log(`Image search response for ${placeName}:`, data);

    if (!data.images || !Array.isArray(data.images)) {
      console.log(`No images found for ${placeName}`);
      throw new Error('No images in response');
    }

    // Convertir les résultats en format PlaceImage
    const images: PlaceImage[] = data.images
      .slice(0, 5)
      .map((img: any, index: number) => ({
        url: img.imageUrl || img.url,
        description: img.title || `${placeName} - Image ${index + 1}`,
        thumbnail: img.thumbnailUrl || img.imageUrl || img.url,
        width: img.imageWidth,
        height: img.imageHeight,
      }))
      .filter((img: PlaceImage) => img.url); // Filtrer les images sans URL

    console.log(`Found ${images.length} images for ${placeName}`);
    return images;
  } catch (error) {
    console.error(`Error fetching images for ${placeName}:`, error);

    // Essayer de trouver une image de fallback
    const placeKey = placeName.toLowerCase();
    for (const [key, images] of Object.entries(fallbackImages)) {
      if (placeKey.includes(key)) {
        console.log(`Using fallback images for ${placeName}`);
        return images;
      }
    }

    // Image de fallback générique pour le Japon
    return [
      {
        url: 'https://images.pexels.com/photos/1829980/pexels-photo-1829980.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
        description: `${placeName} - Japan`,
        thumbnail:
          'https://images.pexels.com/photos/1829980/pexels-photo-1829980.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
      },
    ];
  }
}

export async function serperMapSearch(query: string): Promise<Place[]> {
  console.log('serperMapSearch called with query:', query);

  try {
    // La clé API est définie dans le .env
    const apiKey = process.env.SERPER_API as string;
    console.log(`Using Serper API key: ${apiKey.substring(0, 5)}...`);

    // Utiliser l'en-tête X-API-KEY au lieu du paramètre d'URL
    const url = `https://google.serper.dev/maps?q=${encodeURIComponent(query)}`;
    console.log('Serper API URL:', url);

    console.log('Sending request to Serper API...');
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'X-API-KEY': apiKey,
        'Content-Type': 'application/json',
      },
      redirect: 'follow',
    });

    console.log('Serper API response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Serper API error response:', errorText);
      throw new Error(
        `Network response was not ok. Status: ${response.status} - ${errorText}`,
      );
    }

    console.log('Parsing Serper API response...');
    const data = await response.json();
    console.log(
      'Serper API response data:',
      `${JSON.stringify(data).substring(0, 200)}...`,
    );

    if (!data.places || !Array.isArray(data.places)) {
      console.log('No places found in Serper API response');

      // Données de test pour différentes destinations
      if (
        query.toLowerCase().includes('paris') ||
        query.toLowerCase().includes('tour eiffel')
      ) {
        console.log('Using test data for Paris');
        return [
          {
            cid: 'test1',
            title: 'Tour Eiffel',
            address: 'Champ de Mars, 5 Av. Anatole France, 75007 Paris',
            latitude: 48.8584,
            longitude: 2.2945,
            rating: 4.7,
            category: 'Monument',
            phoneNumber: '+33 1 40 11 23 23',
            website: 'https://www.toureiffel.paris/',
          },
          {
            cid: 'test2',
            title: 'Arc de Triomphe',
            address: 'Place Charles de Gaulle, 75008 Paris',
            latitude: 48.8738,
            longitude: 2.295,
            rating: 4.6,
            category: 'Monument',
            phoneNumber: '+33 1 55 37 73 77',
            website: 'http://www.paris-arc-de-triomphe.fr/',
          },
        ];
      }

      // Données de test pour le Japon/Tokyo
      if (
        query.toLowerCase().includes('japan') ||
        query.toLowerCase().includes('tokyo') ||
        query.toLowerCase().includes('kyoto')
      ) {
        console.log('Using test data for Japan');
        return [
          {
            cid: 'japan1',
            title: 'Godzilla Head',
            address:
              '1 Chome-19 Kabukicho, Shinjuku City, Tokyo 160-0021, Japan',
            latitude: 35.6950521,
            longitude: 139.70191169999998,
            rating: 4.4,
            category: 'Attraction',
            website: 'https://www.toho.co.jp/shinjukutoho/',
            images: [
              {
                url: 'https://images.pexels.com/photos/2070033/pexels-photo-2070033.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
                description: 'Godzilla Head in Shinjuku',
                thumbnail:
                  'https://images.pexels.com/photos/2070033/pexels-photo-2070033.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
              },
            ],
          },
          {
            cid: 'japan2',
            title: 'Tokyo National Museum',
            address: '13-9 Uenokoen, Taito City, Tokyo 110-8712, Japan',
            latitude: 35.7188351,
            longitude: 139.7765215,
            rating: 4.5,
            category: 'Museum',
            phoneNumber: '+81 50-5541-8600',
            website: 'https://www.tnm.jp/',
            images: [
              {
                url: 'https://images.pexels.com/photos/2506923/pexels-photo-2506923.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
                description: 'Tokyo National Museum',
                thumbnail:
                  'https://images.pexels.com/photos/2506923/pexels-photo-2506923.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
              },
            ],
          },
          {
            cid: 'japan3',
            title: 'Shibuya Nonbei Yokocho',
            address: 'Shibuya City, Tokyo, Japan',
            latitude: 35.6581,
            longitude: 139.7014,
            rating: 4.3,
            category: 'Entertainment District',
            images: [
              {
                url: 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
                description: 'Shibuya Nonbei Yokocho',
                thumbnail:
                  'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
              },
            ],
          },
        ];
      }

      return [];
    }

    console.log(`Found ${data.places.length} places in Serper API response`);

    // Récupérer les images pour chaque lieu en parallèle
    const placesWithImages = await Promise.all(
      data.places.map(async (place: any): Promise<Place> => {
        console.log(`Processing place: ${place.title}`);

        // Récupérer les images via l'API Serper Images
        let images: PlaceImage[] = [];

        try {
          console.log(`Fetching images for: ${place.title}`);
          images = await serperImageSearch(place.title, place.address);
        } catch (error) {
          console.error(`Failed to fetch images for ${place.title}:`, error);
          // Utiliser une image de fallback générique
          images = [
            {
              url: 'https://images.pexels.com/photos/1829980/pexels-photo-1829980.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
              description: `${place.title} - Japan`,
              thumbnail:
                'https://images.pexels.com/photos/1829980/pexels-photo-1829980.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
            },
          ];
        }

        console.log(`Total images found for ${place.title}: ${images.length}`);

        const mappedPlace: Place = {
          cid: place.cid || String(Math.random()),
          title: place.title || 'Unknown Location',
          address: place.address || '',
          latitude: place.latitude || 0,
          longitude: place.longitude || 0,
          rating: place.rating || 0,
          category: place.category || 'Place',
          phoneNumber: place.phoneNumber,
          website: place.website,
          images: images.length > 0 ? images : undefined,
        };

        console.log('Final mapped place:', mappedPlace);
        return mappedPlace;
      }),
    );

    return placesWithImages;
  } catch (error) {
    console.error('Error fetching map results:', error);

    // Données de test en cas d'erreur
    if (
      query.toLowerCase().includes('paris') ||
      query.toLowerCase().includes('tour eiffel')
    ) {
      console.log('Using fallback test data for Paris due to error');
      return [
        {
          cid: 'fallback1',
          title: 'Tour Eiffel (Fallback)',
          address: 'Champ de Mars, 5 Av. Anatole France, 75007 Paris',
          latitude: 48.8584,
          longitude: 2.2945,
          rating: 4.7,
          category: 'Monument',
          phoneNumber: '+33 1 40 11 23 23',
          website: 'https://www.toureiffel.paris/',
        },
      ];
    }

    // Données de fallback pour le Japon en cas d'erreur
    if (
      query.toLowerCase().includes('japan') ||
      query.toLowerCase().includes('tokyo') ||
      query.toLowerCase().includes('kyoto')
    ) {
      console.log('Using fallback test data for Japan due to error');
      return [
        {
          cid: 'fallback-japan1',
          title: 'Godzilla Head (Fallback)',
          address: '1 Chome-19 Kabukicho, Shinjuku City, Tokyo 160-0021, Japan',
          latitude: 35.6950521,
          longitude: 139.70191169999998,
          rating: 4.4,
          category: 'Attraction',
          website: 'https://www.toho.co.jp/shinjukutoho/',
          images: [
            {
              url: 'https://images.pexels.com/photos/2070033/pexels-photo-2070033.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
              description: 'Godzilla Head in Shinjuku',
              thumbnail:
                'https://images.pexels.com/photos/2070033/pexels-photo-2070033.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
            },
          ],
        },
        {
          cid: 'fallback-japan2',
          title: 'Tokyo National Museum (Fallback)',
          address: '13-9 Uenokoen, Taito City, Tokyo 110-8712, Japan',
          latitude: 35.7188351,
          longitude: 139.7765215,
          rating: 4.5,
          category: 'Museum',
          phoneNumber: '+81 50-5541-8600',
          website: 'https://www.tnm.jp/',
          images: [
            {
              url: 'https://images.pexels.com/photos/2506923/pexels-photo-2506923.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
              description: 'Tokyo National Museum',
              thumbnail:
                'https://images.pexels.com/photos/2506923/pexels-photo-2506923.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
            },
          ],
        },
      ];
    }

    return [];
  }
}

export type SearchGroupId =
  | 'web'
  | 'x'
  | 'academic'
  | 'youtube'
  | 'reddit'
  | 'analysis'
  | 'chat'
  | 'extreme'
  | 'memory'
  | 'crypto';

export const searchGroups = [
  {
    id: 'web' as const,
    name: 'Web',
    description: 'Search across the entire internet powered by Exa AI',
    icon: Globe,
    show: true,
  },
  {
    id: 'memory' as const,
    name: 'Memory',
    description: 'Your personal memory companion',
    icon: MemoryIcon,
    show: true,
    requireAuth: true,
  },
  {
    id: 'analysis' as const,
    name: 'Analysis',
    description: 'Code, stock and currency stuff',
    icon: CodeIcon,
    show: true,
  },
  {
    id: 'crypto' as const,
    name: 'Crypto',
    description: 'Cryptocurrency research powered by CoinGecko',
    icon: DollarSign,
    show: true,
  },
  {
    id: 'chat' as const,
    name: 'Chat',
    description: 'Talk to the model directly.',
    icon: ChatsCircleIcon,
    show: true,
  },
  {
    id: 'x' as const,
    name: 'X',
    description: 'Search X posts',
    icon: XLogoIcon,
    show: true,
  },
  {
    id: 'reddit' as const,
    name: 'Reddit',
    description: 'Search Reddit posts',
    icon: RedditLogoIcon,
    show: true,
  },
  {
    id: 'academic' as const,
    name: 'Academic',
    description: 'Search academic papers powered by Exa',
    icon: Book,
    show: true,
  },
  {
    id: 'youtube' as const,
    name: 'YouTube',
    description: 'Search YouTube videos powered by Exa',
    icon: YoutubeLogoIcon,
    show: true,
  },
  {
    id: 'extreme' as const,
    name: 'Extreme',
    description: 'Deep research with multiple sources and analysis',
    icon: TelescopeIcon,
    show: true,
  },
] as const;

export type SearchGroup = (typeof searchGroups)[number];

export function invalidateChatsCache() {
  if (typeof window !== 'undefined') {
    const event = new CustomEvent('invalidate-chats-cache');
    window.dispatchEvent(event);
  }
}

export function convertToUIMessages(messages: DBMessage[]): ChatMessage[] {
  return messages.map((message) => ({
    id: message.id,
    role: message.role as 'user' | 'assistant' | 'system',
    parts: message.parts as UIMessagePart<CustomUIDataTypes, ChatTools>[],
    metadata: {
      createdAt: formatISO(message.createdAt),
    },
  }));
}

export function getTextFromMessage(message: ChatMessage): string {
  return message.parts
    .filter((part) => part.type === 'text')
    .map((part) => part.text)
    .join('');
}
